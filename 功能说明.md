# 智能排队管理系统 - 界面切换功能说明

## 功能概述

当前系统已实现了在点击"综合业务"或"个人业务"按钮时，自动隐藏当前界面，切换到外部程序，在相同位置进行鼠标点击，然后切换回当前界面的完整功能。

## 主要功能流程

### 1. 按钮点击处理
- **综合业务按钮** (`btn1`): 点击后触发 `on_btn1_press` 方法
- **个人业务按钮** (`btn2`): 点击后触发 `on_btn2_press` 方法
- 防重复点击机制：使用标志位防止快速重复点击

### 2. 界面切换流程
当用户点击任一业务按钮时，系统执行以下步骤：

1. **获取按钮位置** - `get_button_screen_position()`
   - 计算按钮在屏幕上的绝对坐标
   - 考虑窗口位置和Kivy坐标系转换

2. **隐藏当前界面** - `hide_current_window()`
   - 查找当前Kivy应用窗口
   - 使用Windows API隐藏窗口

3. **显示外部程序** - `show_external_program()`
   - 查找外部程序窗口（支持关键词：getnob, navicat, queue, server2012, zdbit）
   - 如果未找到，尝试启动外部程序
   - 将外部程序窗口置于前台并最大化

4. **执行鼠标点击** - `click_at_position()`
   - 在计算出的屏幕位置执行鼠标左键点击
   - 包含备用点击方法以提高成功率

5. **切换回当前界面** - `bring_to_front()`
   - 将当前Kivy应用窗口重新置于前台
   - 最大化显示

6. **隐藏外部程序** - `hide_external_program()`
   - 延迟执行，确保当前界面完全显示后再隐藏外部程序

## 技术特点

### 窗口管理
- 使用Windows API进行窗口操作
- 支持窗口的显示、隐藏、置前、最大化等操作
- 智能识别窗口（通过标题和类名）

### 坐标转换
- 正确处理Kivy坐标系（左下角原点）到Windows坐标系（左上角原点）的转换
- 考虑窗口在屏幕上的实际位置

### 错误处理
- 完善的异常处理机制
- 多种备用方案确保功能稳定性
- 详细的日志输出便于调试

### 防重复点击
- 使用标志位防止用户快速重复点击
- 自动重置机制（3秒后重置）

## 支持的外部程序

系统可以识别和操作以下外部程序：
- getnob.exe
- 包含"navicat"的程序
- 包含"queue"的程序
- 包含"server2012"的程序
- 包含"zdbit"的程序

## 配置说明

### 外部程序路径
在 `start_external_program()` 方法中配置：
```python
external_programs = [
    "C:/Program Files/ZdBit/Queue/Server2012/getnob.exe",
    "C:/Program Files (x86)/ZdBit/Queue/Server2012/getnob.exe",
    "D:/ZdBit/Queue/Server2012/getnob.exe"
    # 可以添加更多程序路径，按优先级排序
]
```

### 时间延迟设置
各个步骤的等待时间可以根据系统性能调整：
- 窗口隐藏等待：0.3秒
- 外部程序启动等待：1.0秒
- 点击处理等待：0.5秒
- 界面恢复等待：1.0秒

## 使用方法

1. 启动程序，只显示当前界面
2. 点击"综合业务"或"个人业务"按钮
3. 系统自动隐藏当前界面并启动外部程序
4. 在外部程序中执行鼠标点击操作
5. 自动切换回当前界面并终止外部程序

## 日志输出

系统提供详细的日志输出，包括：
- 按钮点击事件
- 窗口切换状态
- 鼠标点击位置
- 错误信息和异常处理

这些日志有助于监控系统运行状态和调试问题。
